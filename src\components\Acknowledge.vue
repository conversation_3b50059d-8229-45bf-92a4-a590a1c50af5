<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnCheckbox from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox.vue'

const props = defineProps({
  isBottom: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '标题',
  },
})
const emits = defineEmits(['studKnow'])
const agent = ref(false)
const timeLeft = ref(30)
const timer = ref(null)
const canCheck = ref(false)

function handleClick() {
  emits('studKnow')
}

// 开始倒计时
function startTimer() {
  timer.value = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--
    }
    else {
      canCheck.value = true
      clearInterval(timer.value)
    }
  }, 1000)
}

onMounted(() => {
  startTimer()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<template>
  <view class="footer fixed bottom-0 w-screen bg-white p-[20rpx] py-[22rpx] text-center">
    <TnCheckbox
      v-model="agent" checked-shape="circle" size="lg" class="text-start"
      :disabled="!canCheck || !props.isBottom"
    >
      <text class="text-[24rpx] font-[400] leading-[39rpx] text-[#191919]">
        我已充分了解<text class="text-[#0065FF]">《{{ title }}》</text>，承诺遵守考生须知的各项规定。
        <text v-if="!canCheck">
          ({{ timeLeft }}s)
        </text>
      </text>
    </TnCheckbox>
    <view class="mt-[20rpx]">
      <TnButton
        type="success"
        :custom-style="{ lineHeight: '45rpx', fontWeight: '400', background: '#04a889', padding: '18rpx', borderRadius: '9rpx', color: '#FFFFFF' }"
        font-size="32rpx" width="95%" :disabled="!agent || !props.isBottom" @click="handleClick"
      >
        {{ props.isBottom ? props.title === '考生须知' ? "我已知晓" : '签署' : "请滚动到底部" }}
      </TnButton>
    </view>
  </view>
</template>

<style scoped lang="scss">
.footer {
  box-shadow: 0rpx -3rpx 8rpx 0rpx rgba(142, 125, 125, 0.13);
}
</style>
